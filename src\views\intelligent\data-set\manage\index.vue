<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据集管理</h2>

    <!-- 主体内容区 -->
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <!-- 操作栏 -->
      <div class="flex justify-between px-10">
        <el-button type="primary" @click="onAdd">新增</el-button>
        <!-- <el-input
          v-model="search"
          placeholder="请输入数据集说明搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <!-- 表格区域 -->
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <el-table-column prop="description" label="数据集说明" show-overflow-tooltip />
          <el-table-column prop="diseaseTypeAnnotation" label="疾病类型" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="projectLeader" label="项目负责人" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column prop="state" label="状态" />
          <el-table-column fixed="right" label="操作" width="200px">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-y-2">
                <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onEdit(row)">修改</el-button>
                <el-popconfirm v-if="row.state !== '废弃'" title="确定停用？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary">停用</el-button>
                  </template>
                </el-popconfirm>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onImport(row)">上传数据集</el-button>
                <el-button link type="primary" @click="onHistory(row)"> 历史数据集 </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="showAdd"
    :title="formTitle"
    width="600px"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onAddClose"
  >
    <div v-if="uploadLoading" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <DatasetForm
      v-else
      ref="datasetFormRef"
      show-database
      :show-metadata="showMetadata"
      @change-show-meatadata="(e) => (showMetadata = e)"
    />
    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 上传数据集弹窗 -->
  <el-dialog
    v-model="showDatabase"
    title="上传数据集"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onDatabaseClose"
  >
    <div v-show="!startUpload">
      <el-form ref="dbFormRef" label-position="top" :model="dbForm" :rules="dbRules">
        <el-form-item label="数据集文件" prop="dbset">
          <el-upload v-model:file-list="dbForm.dbset" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="startUpload" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button @click="onDatabaseClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onDatabaseConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 历史数据集弹窗 -->
  <HistoryDatasetDialog v-if="showHistory" v-model="showHistory" :current-row="currentRow!" />

  <!-- 上传进度弹窗 -->
  <el-dialog
    v-model="showUploadProgress"
    title="上传进度"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="stopUploadProgressPolling"
  >
    <div class="upload-progress-container">
      <div v-if="uploadProgressData" class="progress-content">
        <div v-for="(messages, queueId) in uploadProgressData" :key="queueId" class="queue-progress">
          <h4 class="queue-title">任务队列: {{ queueId }}</h4>
          <div class="messages-list">
            <div v-for="(message, index) in messages" :key="index" class="message-item">
              <div class="message-time">{{ message.createTime || '处理中...' }}</div>
              <div class="message-content">{{ message.message || '正在处理...' }}</div>
              <div v-if="message.progress" class="message-progress">
                <el-progress :percentage="message.progress" :show-text="true" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="loading-content">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在获取上传进度...</span>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button
          @click="
            showUploadProgress = false;
            stopUploadProgressPolling();
          "
          >关闭</el-button
        >
      </span>
    </template>
  </el-dialog>

  <VerifyDialog :id="verifyId" v-model="showVerify" @success="onVerifySuccess" />
</template>

<script setup lang="ts">
  // 导入所需的依赖
  import {
    findFileInforByUserId,
    deleteEntityById_36,
    findAllDb,
    newOrUpdateEntity_10,
    uploadFileExt,
    exportMedicalDataSetToDatabaseExt,
    hasFileMessages,
    getFileMessages,
    getFileMessages_1,
  } from '@/api';
  import { WarningFilled, Loading } from '@element-plus/icons-vue';
  import { ElMessage, FormInstance, UploadFiles } from 'element-plus';
  import { useRouter } from 'vue-router';
  import DatasetForm from '../components/DatasetForm.vue';
  import VerifyDialog from '../components/VerifyDialog.vue';
  import dayjs from 'dayjs';
  import HistoryDatasetDialog from '../components/HistoryDatasetDialog.vue';
  import { useUsers } from '@/store/index';
  import { generateUUID } from '@/utils/crypto';
  import { upload } from '@/utils/request';
  const store = useUsers();

  // 基础数据
  const router = useRouter();
  const loading = ref(false);
  const currentRow = ref<FileInfoVO | null>(null);
  const search = ref('');
  const importText = ref('');

  // 日志和错误处理
  interface LogMessage {
    text: string;
    isError: boolean;
  }
  const logMessages = ref<LogMessage[]>([]);
  const errorMessages = ref<string[]>([]);
  const hasErrors = computed(() => errorMessages.value.length > 0);

  // 搜索相关
  const onSearch = () => fetchData();

  // 表格数据
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 新增/编辑表单相关
  const datasetFormRef = ref<any>(null);
  const showMetadata = ref(true);
  const showAdd = ref(false);
  const addLoading = ref(false);

  const formTitle = computed(() => {
    if (datasetFormRef.value) {
      return datasetFormRef.value.addForm.id ? '编辑数据集' : '新增数据集';
    }
    return '';
  });

  // 上传数据集相关
  const uploadLoading = ref(false);
  const showDatabase = ref(false);
  const databaseValue = ref(0);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const percentage = ref(0);
  const startUpload = ref(false);
  const dbFormRef = ref<FormInstance>();
  const dbForm = reactive({
    dbset: [] as UploadFiles,
  });
  const dbRules = ref({
    dbset: [{ required: true, message: '不能为空' }],
  });

  // 保留用于兼容性
  const sseResult = ref('');

  // 校验数据相关
  const showVerify = ref(false);
  const verifyId = ref(0);

  // 上传任务查询和进度相关
  const uploadTaskPolling = ref<NodeJS.Timeout | null>(null);
  const currentQueueId = ref('');
  const showUploadProgress = ref(false);
  const uploadProgressData = ref<any>(null);

  // 添加日志消息的方法
  const addLogMessage = (text: string) => {
    const isError = text.includes('错误提示') || text.includes('失败');
    logMessages.value.push({ text, isError });

    // 如果是错误消息，添加到错误消息列表
    if (isError) {
      errorMessages.value.push(text);
    }

    // 保持滚动到最新消息
    nextTick(() => {
      const logContainer = document.querySelector('.log-messages');
      if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    });
  };

  // 重置日志和错误信息
  const resetLogs = () => {
    logMessages.value = [];
    errorMessages.value = [];
    importText.value = '';
  };

  // 查询是否有正在进行的上传任务
  const checkUploadTask = async (queueId?: string) => {
    try {
      if (queueId) {
        const params = {
          userId: store.user.id.toString(),
          queueId,
        };
        const { data } = await hasFileMessages(params);
        return data;
      } else {
        // 查询用户的所有上传任务
        const params = {
          userId: store.user.id.toString(),
        };
        const { data } = await getFileMessages(params);
        return data && Object.keys(data).length > 0;
      }
    } catch (error) {
      console.error('查询上传任务失败:', error);
      return false;
    }
  };

  // 获取上传进度信息
  const getUploadProgress = async (queueId?: string) => {
    try {
      if (queueId) {
        const params = {
          userId: store.user.id.toString(),
          queueId,
        };
        const { data } = await getFileMessages_1(params);
        return data;
      } else {
        const params = {
          userId: store.user.id.toString(),
        };
        const { data } = await getFileMessages(params);
        return data;
      }
    } catch (error) {
      console.error('获取上传进度失败:', error);
      return null;
    }
  };

  // 开始轮询上传进度
  const startUploadProgressPolling = (queueId: string) => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
    }

    let pollCount = 0;
    const maxPollCount = 150; // 最多轮询5分钟 (150 * 2秒)

    uploadTaskPolling.value = setInterval(async () => {
      pollCount++;

      // 防止无限轮询
      if (pollCount > maxPollCount) {
        stopUploadProgressPolling();
        ElMessage({ type: 'warning', message: '上传任务超时，请手动刷新页面查看结果' });
        return;
      }

      const progressData = await getUploadProgress(queueId);
      if (progressData) {
        uploadProgressData.value = progressData;

        // 检查是否完成或失败
        const messages = Object.values(progressData).flat() as any[];
        const hasCompleted = messages.some(
          (msg: any) => msg.message && (msg.message.includes('完成') || msg.message.includes('成功'))
        );
        const hasFailed = messages.some(
          (msg: any) => msg.message && (msg.message.includes('失败') || msg.message.includes('错误'))
        );

        if (hasCompleted) {
          stopUploadProgressPolling();
          showUploadProgress.value = false;
          ElMessage({ type: 'success', message: '上传完成' });
          fetchData();
        } else if (hasFailed) {
          stopUploadProgressPolling();
          ElMessage({ type: 'error', message: '上传失败，请查看详细信息' });
        }
      }
    }, 2000); // 每2秒查询一次
  };

  // 停止轮询上传进度
  const stopUploadProgressPolling = () => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
      uploadTaskPolling.value = null;
    }
  };

  // 方法定义
  const fetchData = async (pageNum = 1) => {
    try {
      loading.value = true;
      const { data } = await findFileInforByUserId(store.user.id, pageNum, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const onAdd = () => {
    resetLogs();
    showMetadata.value = true;
    showAdd.value = true;
  };

  const onEdit = async (row: FileInfoVO) => {
    resetLogs();
    showMetadata.value = false;
    showAdd.value = true;
    nextTick(() => {
      if (row) {
        Object.keys(datasetFormRef.value.addForm).forEach((key) => {
          if (datasetFormRef.value.addForm[key] !== null && datasetFormRef.value.addForm[key] !== undefined) {
            datasetFormRef.value.addForm[key] = row[key];
          }
        });
      }
    });
  };

  const onAddClose = () => {
    stopUploadProgressPolling();
    addLoading.value = false;
    showAdd.value = false;
    datasetFormRef.value?.formRef?.resetFields();
    uploadLoading.value = false;
    resetLogs();
  };

  const onAddConfirm = async () => {
    sseResult.value = '';
    if (uploadLoading.value) {
      onAddClose();
      return;
    }

    if (!datasetFormRef.value.formRef) {
      ElMessage({ type: 'warning', message: '请先导入元数据' });
      return;
    }

    const valid = await datasetFormRef.value.formRef.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      resetLogs();

      if (!datasetFormRef.value.addForm.id) {
        // 新增 - 先检查是否有正在进行的上传任务
        addLogMessage('正在检查上传任务状态...');
        const hasRunningTask = await checkUploadTask();
        if (hasRunningTask) {
          ElMessage({
            type: 'warning',
            message: '检测到正在进行的上传任务，将显示进度窗口',
            duration: 3000,
          });
          showUploadProgress.value = true;
          const progressData = await getUploadProgress();
          uploadProgressData.value = progressData;
          startUploadProgressPolling('');
          return;
        }

        uploadLoading.value = true;
        // 生成队列ID
        const queueId = generateUUID();
        currentQueueId.value = queueId;

        const databaseId = datasetFormRef.value.addForm.databaseId;

        // 使用直接API请求而不是SSE
        const params = {
          cbdDatabaseId: databaseId,
          userIdCur: store.user.id,
        };

        addLogMessage('开始上传元数据...');
        // await uploadFileExt(params);
        const formData = new FormData();
        formData.append('mddFile', file);
        await upload('/FileInfor/uploadFileId/hasmddExt', {
          method: 'post',
          data: formData,
        });

        // 开始轮询进度
        startUploadProgressPolling(queueId);
        showUploadProgress.value = true;

        ElMessage({
          type: 'success',
          message: '上传任务已启动，正在处理中...',
          duration: 3000,
        });
      } else {
        // 编辑
        const form = {
          ...datasetFormRef.value.addForm,
          createDate: dayjs(datasetFormRef.value.addForm.createDate).format('YYYY-MM-DD'),
        };
        await newOrUpdateEntity_10(form);
        onAddClose();
        ElMessage({ type: 'success', message: '操作成功' });
        fetchData();
      }
    } catch (error) {
      console.error(error);
      ElMessage({ type: 'error', message: '操作失败，请重试' });
    } finally {
      addLoading.value = false;
    }
  };

  const onViewDetail = (row: FileInfoVO) => {
    router.push({ name: 'DatasetField', params: { id: row.id } });
  };

  const onDel = async (row: FileInfoVO) => {
    try {
      loading.value = true;
      await deleteEntityById_36(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const onVerifySuccess = () => {
    fetchDatabase();
    databaseValue.value = 0;
    showDatabase.value = true;
  };

  const onImport = (row: FileInfoVO) => {
    resetLogs();
    currentRow.value = row;
    showDatabase.value = true;
  };

  const showHistory = ref(false);
  const onHistory = (row: FileInfoVO) => {
    currentRow.value = row;
    showHistory.value = true;
  };

  const onDatabaseClose = () => {
    stopUploadProgressPolling();
    startUpload.value = false;
    uploadLoading.value = false; // 重置上传加载状态
    dbFormRef.value?.resetFields();
    showDatabase.value = false;
    addLoading.value = false;
    resetLogs();
  };

  const fetchDatabase = async () => {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.error(error);
    }
  };

  const onDatabaseConfirm = async () => {
    sseResult.value = '';
    if (uploadLoading.value) {
      onDatabaseClose();
      return;
    }

    const valid = await dbFormRef.value?.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      startUpload.value = true;
      percentage.value = 0;
      resetLogs();

      // 先检查是否有正在进行的上传任务
      addLogMessage('正在检查上传任务状态...');
      const hasRunningTask = await checkUploadTask();
      if (hasRunningTask) {
        ElMessage({
          type: 'warning',
          message: '检测到正在进行的上传任务，将显示进度窗口',
          duration: 3000,
        });
        showUploadProgress.value = true;
        const progressData = await getUploadProgress();
        uploadProgressData.value = progressData;
        startUploadProgressPolling('');
        return;
      }

      // 生成队列ID
      const queueId = generateUUID();
      currentQueueId.value = queueId;

      if (!currentRow.value?.id) {
        throw new Error('未选择数据集');
      }

      const params = {
        queueId: queueId,
        fileId: currentRow.value.id,
        userIdCur: store.user.id.toString(),
      };

      addLogMessage('开始上传数据集...');
      await exportMedicalDataSetToDatabaseExt(params);

      // 开始轮询进度
      startUploadProgressPolling(queueId);
      showUploadProgress.value = true;

      ElMessage({
        type: 'success',
        message: '上传任务已启动，正在处理中...',
        duration: 3000,
      });

      // 上传完成后设置 uploadLoading 为 true，这样再次点击确定时会直接关闭弹窗
      uploadLoading.value = true;
    } catch (error) {
      console.error('数据库导入失败:', error);
      ElMessage({ type: 'error', message: '上传失败，请重试' });
    } finally {
      addLoading.value = false;
    }
  };

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });

  onUnmounted(() => {
    stopUploadProgressPolling();
    startUpload.value = false;
  });
</script>

<style lang="scss" scoped>
  .vertical-radio-group {
    display: block;
    .el-radio {
      display: block;
    }
  }

  .log-container {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
  }

  .log-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-family: monospace;
    background-color: #f8f9fa;
    line-height: 1.5;
    font-size: 14px;
  }

  .error-message {
    color: #f56c6c;
    font-weight: 500;
  }

  .error-summary {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff0f0;
    border-top: 1px solid #fde2e2;
  }

  .error-title {
    display: flex;
    align-items: center;
    color: #f56c6c;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;

    .el-icon {
      margin-right: 6px;
    }
  }

  .error-list {
    max-height: 100px;
    overflow-y: auto;

    p {
      margin: 4px 0;
      padding-left: 16px;
      font-size: 13px;
      color: #f56c6c;
    }
  }

  .upload-progress-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .progress-content {
    .queue-progress {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 15px;

      .queue-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .messages-list {
        .message-item {
          margin-bottom: 10px;
          padding: 8px;
          background-color: #f8f9fa;
          border-radius: 4px;

          .message-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .message-content {
            font-size: 14px;
            color: #303133;
            margin-bottom: 8px;
          }

          .message-progress {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 14px;
    color: #606266;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
</style>
